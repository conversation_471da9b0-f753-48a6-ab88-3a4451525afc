package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import sdk.entity.AndroidDevice;
import ui.base.picture.ADBPanel;
import ui.base.picture.AndroidPictureRectDrawLabel;
import ui.base.picture.PictureRectDrawLabel;
import ui.base.picture.ScaledPoint;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class AndroidPictureContainer extends PictureContainer implements AppObserver {
    private JLabel screenShotButton;

    // 视频流相关字段
    private InputStream videoInputStream;
    private SwingWorker<Boolean, BufferedImage> videoStreamWorker;
    private final AtomicBoolean isStreamRunning = new AtomicBoolean(false);

    // NAL单元队列，用于缓存解码
    private final BlockingQueue<byte[]> nalQueue = new LinkedBlockingQueue<>(100);

    public AndroidPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
    }

    public AndroidPictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device, alwaysDynamic);
    }

    @Override
    public void createView() {
        screenShotButton = new JLabel(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.takePhotoIconPath));
        super.createView();
        setPlayOrPauseButtonVisible(false);
        JTabbedPane tabbedPane = new JTabbedPane();
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(getPicturePanel(), BorderLayout.CENTER);
        panel.add(getToolBox(), BorderLayout.SOUTH);
        tabbedPane.addTab("Android", panel);
        tabbedPane.addTab("ADB命令面板", new ADBPanel(getMainModel(), getDevice()));
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);
    }

    @Override
    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new AndroidPictureRectDrawLabel(getMainModel(), this);
    }

    @Override
    protected List<JLabel> toolButtonList() {
        return Collections.singletonList(screenShotButton);
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getAppModel().registerObserver(this);
    }

    private void screenShot() {
        initScreenshotTasks().execute();
    }

    private SwingWorker<Void, Void> initScreenshotTasks() {
        return new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                screenShotButton.setEnabled(false);
                screenShotButton.repaint();
                AndroidDevice androidDevice = (AndroidDevice) getDevice();
                OperationResult operationResult = androidDevice.screenshot();
                if (operationResult.isOk()) {
                    String filePath = (String) operationResult.getData();
                    try {
                        BufferedImage bufferedImage = ImageIO.read(new File(filePath));
                        getPicturePanel().setImageStream(bufferedImage);
                    } catch (IOException e) {
                        SwingUtil.showWarningDialog(AndroidPictureContainer.this, e.getMessage());
                    }
                } else {
                    SwingUtil.showWarningDialog(AndroidPictureContainer.this, operationResult.getMessage());
                }
                return null;
            }

            @Override
            protected void done() {
                screenShotButton.setEnabled(true);
                screenShotButton.repaint();
            }
        };
    }

    @Override
    public void createActions() {
        super.createActions();
        screenShotButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (screenShotButton.isEnabled()) {
                    screenShot();
                }
            }
        });
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    public void grab() {
        AndroidDevice device = (AndroidDevice) getDevice();
        String deviceName = device.getDeviceName();

        // 如果已经有视频流在运行，先停止
        if (isStreamRunning.get()) {
            stopVideoStream();
        }

        log.info("开始启动Android设备视频流: {}", deviceName);

        videoStreamWorker = new SwingWorker<Boolean, BufferedImage>() {
            private H264StreamParser parser;
            private FFmpegFrameGrabber tempGrabber;
            private Java2DFrameConverter converter;

            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    startGrab();
                    publish((BufferedImage) null); // 清空显示
                    getPicturePanel().setText("正在连接Android设备视频流: " + deviceName);

                    videoInputStream = device.videoStream();
                    if (videoInputStream == null) {
                        getPicturePanel().setText("无法启动视频流: 设备返回null");
                        return false;
                    }

                    isStreamRunning.set(true);
                    getPicturePanel().clearText();
                    completeGrab();

                    log.info("Android视频流启动成功，开始读取H.264视频帧: {}", deviceName);

                    // 初始化解码组件
                    parser = new H264StreamParser();
                    converter = new Java2DFrameConverter();

                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytesRead = 0;
                    int frameCount = 0;

                    while (isStreamRunning.get() && !isCancelled()) {
                        try {
                            bytesRead = videoInputStream.read(buffer);
                            if (bytesRead == -1) {
                                log.warn("视频流结束: {}", deviceName);
                                break;
                            }

                            totalBytesRead += bytesRead;
                            parser.addData(buffer, bytesRead);

                            // 提取并解码NAL单元
                            byte[] nalUnit;
                            while ((nalUnit = parser.extractNalUnit()) != null && !isCancelled()) {
                                BufferedImage frame = decodeNalUnitSync(nalUnit);
                                if (frame != null) {
                                    frameCount++;
                                    publish(frame); // 在SwingWorker内部调用publish

                                    if (frameCount % 50 == 0) {
                                        log.debug("已处理 {} 帧", frameCount);
                                    }
                                }
                            }

                            // 每1MB数据打印一次日志
                            if (totalBytesRead % (1024 * 1024) == 0) {
                                log.debug("已读取 {}MB 数据", totalBytesRead / (1024 * 1024));
                            }

                        } catch (Exception e) {
                            if (isStreamRunning.get()) {
                                log.error("读取H.264视频流失败: {}, 错误: {}", deviceName, e.getMessage());
                                Thread.sleep(100);
                            }
                        }
                    }

                    log.info("视频流读取完成: {}, 总共读取: {}MB, 处理帧数: {}",
                            deviceName, totalBytesRead / (1024 * 1024), frameCount);
                    return true;

                } catch (Exception e) {
                    log.error("Android视频流获取失败: {}, 错误: {}", deviceName, e.getMessage(), e);
                    getPicturePanel().setText("视频流获取失败: " + e.getMessage());
                    return false;
                } finally {
                    // 清理资源
                    if (converter != null) {
                        try {
                            converter.close();
                        } catch (Exception e) {
                            log.warn("清理转换器失败", e);
                        }
                    }
                    if (tempGrabber != null) {
                        try {
                            tempGrabber.close();
                        } catch (Exception e) {
                            log.warn("清理临时解码器失败", e);
                        }
                    }
                }
            }

            @Override
            protected void process(List<BufferedImage> chunks) {
                if (!chunks.isEmpty() && getPicturePanel() != null) {
                    // 只处理最新的帧，避免积压
                    BufferedImage latestFrame = chunks.get(chunks.size() - 1);
                    if (latestFrame != null) {
                        getPicturePanel().setImageStream(latestFrame);
                    }
                }
            }

            @Override
            protected void done() {
                try {
                    Boolean result = get();
                    if (!result) {
                        log.warn("Android视频流获取任务完成，但结果为失败");
                        getPicturePanel().setText("视频流启动失败，请检查设备连接状态");
                    }
                } catch (Exception e) {
                    log.error("Android视频流获取任务异常", e);
                    getPicturePanel().setText("视频流启动异常: " + e.getMessage());
                } finally {
                    stopVideoStream();
                }
            }

            /**
             * 同步解码单个NAL单元（在SwingWorker内部调用）
             */
            private BufferedImage decodeNalUnitSync(byte[] nalUnit) {
                try {
                    // 使用ByteArrayInputStream创建临时输入流
                    try (ByteArrayInputStream inputStream = new ByteArrayInputStream(nalUnit)) {
                        // 重用或创建临时解码器
                        if (tempGrabber == null) {
                            tempGrabber = new FFmpegFrameGrabber(inputStream);
                            tempGrabber.setFormat("h264");
                            tempGrabber.setOption("probesize", "4096");
                            tempGrabber.setOption("analyzeduration", "0");
                            tempGrabber.setOption("fflags", "nobuffer");
                            tempGrabber.start();
                        } else {
                            // 重置输入流
                            tempGrabber.close();
                            tempGrabber = new FFmpegFrameGrabber(inputStream);
                            tempGrabber.setFormat("h264");
                            tempGrabber.setOption("probesize", "4096");
                            tempGrabber.setOption("analyzeduration", "0");
                            tempGrabber.setOption("fflags", "nobuffer");
                            tempGrabber.start();
                        }

                        Frame frame = tempGrabber.grabImage();
                        if (frame != null && converter != null) {
                            return converter.convert(frame);
                        }
                    }
                } catch (Exception e) {
                    log.debug("解码NAL单元失败: {}", e.getMessage());
                    // 重置解码器状态
                    if (tempGrabber != null) {
                        try {
                            tempGrabber.close();
                            tempGrabber = null;
                        } catch (Exception ex) {
                            log.debug("关闭损坏的解码器失败", ex);
                        }
                    }
                }
                return null;
            }
        };

        videoStreamWorker.execute();
    }

    /**
     * 简化的停止视频流方法
     */
    private void stopVideoStream() {
        isStreamRunning.set(false);

        // 清空NAL队列（如果还在使用）
        nalQueue.clear();

        // 关闭视频流输入流
        if (videoInputStream != null) {
            try {
                videoInputStream.close();
                log.info("Android视频流输入流已关闭: {}", getDevice().getDeviceName());
            } catch (Exception e) {
                log.warn("关闭Android视频流输入流失败: {}, 错误: {}", getDevice().getDeviceName(), e.getMessage());
            } finally {
                videoInputStream = null;
            }
        }

        // 取消工作线程
        if (videoStreamWorker != null && !videoStreamWorker.isDone()) {
            videoStreamWorker.cancel(true);
        }

        log.info("Android视频流已停止: {}", getDevice().getDeviceName());
    }



    @Override
    public void appExit() {
        stopVideoStream();
    }


    /**
     * H.264流解析器
     */
    private static class H264StreamParser {
        private final ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        private static final byte[] NAL_START_CODE_4 = {0x00, 0x00, 0x00, 0x01};
        private static final byte[] NAL_START_CODE_3 = {0x00, 0x00, 0x01};

        public void addData(byte[] data, int length) {
            buffer.write(data, 0, length);
        }

        public byte[] extractNalUnit() {
            byte[] data = buffer.toByteArray();
            if (data.length < 4) {
                return null;
            }

            // 查找第一个NAL开始码
            int firstNalStart = findNalStartCode(data, 0);
            if (firstNalStart == -1) {
                return null;
            }

            // 查找下一个NAL开始码
            int nextNalStart = findNalStartCode(data, firstNalStart + 4);
            if (nextNalStart == -1) {
                // 没找到下一个NAL单元，检查是否有足够数据
                if (data.length - firstNalStart > 1024) { // 如果超过1KB，认为是完整NAL单元
                    byte[] nalUnit = new byte[data.length - firstNalStart];
                    System.arraycopy(data, firstNalStart, nalUnit, 0, nalUnit.length);
                    buffer.reset(); // 清空缓冲区
                    return nalUnit;
                }
                return null;
            }

            // 提取NAL单元
            int nalLength = nextNalStart - firstNalStart;
            byte[] nalUnit = new byte[nalLength];
            System.arraycopy(data, firstNalStart, nalUnit, 0, nalLength);

            // 移除已提取的数据
            byte[] remaining = new byte[data.length - nextNalStart];
            System.arraycopy(data, nextNalStart, remaining, 0, remaining.length);
            buffer.reset();
            try {
                buffer.write(remaining);
            } catch (IOException e) {
                log.error("重写缓冲区失败", e);
            }

            return nalUnit;
        }

        private int findNalStartCode(byte[] data, int startPos) {
            // 查找4字节开始码
            for (int i = startPos; i <= data.length - 4; i++) {
                if (data[i] == 0x00 && data[i + 1] == 0x00 &&
                        data[i + 2] == 0x00 && data[i + 3] == 0x01) {
                    return i;
                }
            }

            // 查找3字节开始码
            for (int i = startPos; i <= data.length - 3; i++) {
                if (data[i] == 0x00 && data[i + 1] == 0x00 && data[i + 2] == 0x01) {
                    return i;
                }
            }

            return -1;
        }
    }
}