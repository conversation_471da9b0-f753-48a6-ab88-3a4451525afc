package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import sdk.entity.AdbHudDevice;
import sdk.entity.AndroidDevice;
import ui.base.picture.AdbSendPanel;
import ui.base.picture.AndroidPictureRectDrawLabel;
import ui.base.picture.PictureRectDrawLabel;
import ui.base.picture.ScaledPoint;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Collections;
import java.util.List;

@Slf4j
public class AdbHudPictureContainer extends PictureContainer implements AppObserver {
    private JLabel screenShotButton;
    // 视频流相关字段
    private InputStream videoInputStream;
    private SwingWorker<Boolean, BufferedImage> videoStreamWorker;
    private volatile boolean isStreamRunning = false;

    // H.264解码器相关字段
    private FFmpegFrameGrabber h264Decoder;
    private Java2DFrameConverter frameConverter;

    public AdbHudPictureContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
    }

    public AdbHudPictureContainer(ClientView clientView, MainModel mainModel, Device device, boolean alwaysDynamic) {
        super(clientView, mainModel, device, alwaysDynamic);
    }


    @Override
    public void createView() {
        screenShotButton = new JLabel(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.takePhotoIconPath));
        super.createView();
        setPlayOrPauseButtonVisible(false);
        JTabbedPane tabbedPane = new JTabbedPane();
        JPanel panel = new JPanel(new BorderLayout());
        panel.add(getPicturePanel(), BorderLayout.CENTER);
        panel.add(getToolBox(), BorderLayout.SOUTH);
        AdbSendPanel adbSendPanel = new AdbSendPanel(this, getMainModel());
        tabbedPane.addTab("AdbHud", panel);
        tabbedPane.addTab("发送ADB指令", adbSendPanel);
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);

    }

    @Override
    protected PictureRectDrawLabel getPictureRectDrawLabel() {
        return new AndroidPictureRectDrawLabel(getMainModel(), this);
    }

    @Override
    protected List<JLabel> toolButtonList() {
        return Collections.singletonList(screenShotButton);
    }

    @Override
    public void registerModelObservers() {
        getMainModel().getAppModel().registerObserver(this);
    }

    private void screenShot() {
        initScreenshotTasks().execute();
    }

    private SwingWorker<Void, Void> initScreenshotTasks() {
        return new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() {
                screenShotButton.setEnabled(false);
                screenShotButton.repaint();
                AdbHudDevice adbHudDevice = (AdbHudDevice) getDevice();
                OperationResult operationResult = adbHudDevice.screenshot();
                if (operationResult.isOk()) {
                    String filePath = (String) operationResult.getData();
                    try {
                        BufferedImage bufferedImage = ImageIO.read(new File(filePath));
                        getPicturePanel().setImageStream(bufferedImage);
                    } catch (IOException e) {
                        SwingUtil.showWarningDialog(AdbHudPictureContainer.this, e.getMessage());
                    }
                } else {
                    SwingUtil.showWarningDialog(AdbHudPictureContainer.this, operationResult.getMessage());
                }
                return null;
            }

            @Override
            protected void done() {
                screenShotButton.setEnabled(true);
                screenShotButton.repaint();
            }
        };
    }

    @Override
    public void createActions() {
        super.createActions();
        screenShotButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (screenShotButton.isEnabled()) {
                    screenShot();
                }
            }
        });
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    @Override
    public void grab() {
        AndroidDevice device = (AndroidDevice) getDevice();
        String deviceName = device.getDeviceName();

        // 如果已经有视频流在运行，先停止
        if (isStreamRunning) {
            stopVideoStream();
        }

        log.info("开始启动Android设备视频流: {}", deviceName);

        videoStreamWorker = new SwingWorker<Boolean, BufferedImage>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    startGrab();
                    publish((BufferedImage) null); // 清空显示
                    getPicturePanel().setText("正在连接Android设备视频流: " + deviceName);

                    videoInputStream = device.videoStream();
                    if (videoInputStream == null) {
                        getPicturePanel().setText("无法启动视频流: 设备返回null");
                        return false;
                    }

                    isStreamRunning = true;
                    getPicturePanel().clearText();
                    completeGrab();

                    log.info("Android视频流启动成功，开始读取H.264视频帧: {}", deviceName);

                    // 初始化H.264解码器
                    initH264Decoder();

                    // 使用字节流方式读取H.264视频帧
                    ByteArrayOutputStream h264Buffer = new ByteArrayOutputStream();
                    byte[] buffer = new byte[4096];
                    int bytesRead;

                    while (isStreamRunning && !isCancelled()) {
                        try {
                            while ((bytesRead = videoInputStream.read(buffer)) != -1) {
                                h264Buffer.write(buffer, 0, bytesRead);

                                // 检查是否收到完整的H.264 NAL单元
                                byte[] data = h264Buffer.toByteArray();
                                if (isCompleteH264Frame(data)) {
                                    // 解析H.264数据为BufferedImage
                                    BufferedImage frame = parseH264Frame(data);
                                    if (frame != null) {
                                        publish(frame);
                                    }
                                    h264Buffer.reset();
                                }

                                // 防止缓冲区过大
                                if (h264Buffer.size() > 8 * 1024 * 1024) { // 8MB限制
                                    log.warn("H.264视频流缓冲区过大，重置: {} bytes", h264Buffer.size());
                                    h264Buffer.reset();
                                }
                            }
                        } catch (Exception e) {
                            if (isStreamRunning) {
                                log.error("读取H.264视频帧失败: {}, 错误: {}", deviceName, e.getMessage());
                                Thread.sleep(100); // 错误后短暂休眠
                            }
                        }
                    }

                    return true;
                } catch (Exception e) {
                    log.error("Android视频流获取失败: {}, 错误: {}", deviceName, e.getMessage(), e);
                    getPicturePanel().setText("视频流获取失败: " + e.getMessage());
                    return false;
                }
            }

            @Override
            protected void process(List<BufferedImage> chunks) {
                if (!chunks.isEmpty() && getPicturePanel() != null) {
                    BufferedImage latestFrame = chunks.get(chunks.size() - 1);
                    if (latestFrame != null) {
                        getPicturePanel().setImageStream(latestFrame);
                    }
                }
            }

            @Override
            protected void done() {
                try {
                    Boolean result = get();
                    if (!result) {
                        log.warn("Android视频流获取任务完成，但结果为失败");
                        getPicturePanel().setText("视频流启动失败，请检查设备连接状态");
                    }
                } catch (Exception e) {
                    log.error("Android视频流获取任务异常", e);
                    getPicturePanel().setText("视频流启动异常: " + e.getMessage());
                } finally {
                    stopVideoStream();
                }
            }
        };

        videoStreamWorker.execute();
    }

    /**
     * 停止视频流
     */
    private void stopVideoStream() {
        isStreamRunning = false;

        // 关闭H.264解码器
        cleanupH264Decoder();

        // 关闭视频流输入流
        if (videoInputStream != null) {
            try {
                videoInputStream.close();
                log.info("Android视频流输入流已关闭: {}", getDevice().getDeviceName());
            } catch (Exception e) {
                log.warn("关闭Android视频流输入流失败: {}, 错误: {}", getDevice().getDeviceName(), e.getMessage());
            } finally {
                videoInputStream = null;
            }
        }

        // 取消工作线程
        if (videoStreamWorker != null && !videoStreamWorker.isDone()) {
            videoStreamWorker.cancel(true);
        }

        log.info("Android视频流已停止: {}", getDevice().getDeviceName());
    }

    @Override
    public void appExit() {
        stopVideoStream();
    }

    /**
     * 初始化H.264解码器
     */
    private void initH264Decoder() {
        try {
            if (frameConverter == null) {
                frameConverter = new Java2DFrameConverter();
            }
            log.info("H.264解码器初始化成功");
        } catch (Exception e) {
            log.error("H.264解码器初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理H.264解码器资源
     */
    private void cleanupH264Decoder() {
        try {
            if (h264Decoder != null) {
                h264Decoder.close();
                h264Decoder = null;
            }
            if (frameConverter != null) {
                frameConverter.close();
                frameConverter = null;
            }
            log.info("H.264解码器资源已清理");
        } catch (Exception e) {
            log.warn("清理H.264解码器资源失败: {}", e.getMessage());
        }
    }

    /**
     * 检查是否为完整的H.264 NAL单元
     *
     * @param data 字节数据
     * @return 是否为完整帧
     */
    private boolean isCompleteH264Frame(byte[] data) {
        if (data.length < 4) {
            return false;
        }

        // 查找H.264 NAL单元开始标识 (0x00000001 或 0x000001)
        int nalStartIndex = -1;
        for (int i = 0; i <= data.length - 4; i++) {
            if ((data[i] & 0xFF) == 0x00 && (data[i + 1] & 0xFF) == 0x00 &&
                (data[i + 2] & 0xFF) == 0x00 && (data[i + 3] & 0xFF) == 0x01) {
                nalStartIndex = i;
                break;
            }
        }

        // 如果没找到4字节标识，查找3字节标识
        if (nalStartIndex == -1) {
            for (int i = 0; i <= data.length - 3; i++) {
                if ((data[i] & 0xFF) == 0x00 && (data[i + 1] & 0xFF) == 0x00 &&
                    (data[i + 2] & 0xFF) == 0x01) {
                    nalStartIndex = i;
                    break;
                }
            }
        }

        if (nalStartIndex == -1) {
            return false; // 没有找到NAL单元开始标识
        }

        // 查找下一个NAL单元开始标识，表示当前NAL单元结束
        for (int i = nalStartIndex + 4; i <= data.length - 4; i++) {
            if ((data[i] & 0xFF) == 0x00 && (data[i + 1] & 0xFF) == 0x00 &&
                (data[i + 2] & 0xFF) == 0x00 && (data[i + 3] & 0xFF) == 0x01) {
                return true; // 找到完整的NAL单元
            }
        }

        // 检查3字节标识
        for (int i = nalStartIndex + 3; i <= data.length - 3; i++) {
            if ((data[i] & 0xFF) == 0x00 && (data[i + 1] & 0xFF) == 0x00 &&
                (data[i + 2] & 0xFF) == 0x01) {
                return true; // 找到完整的NAL单元
            }
        }

        // 如果数据足够大，可能是完整的单个NAL单元
        return data.length > 1024; // 简单的启发式判断
    }

    /**
     * 解析H.264数据为BufferedImage
     *
     * @param h264Data H.264字节数据
     * @return BufferedImage或null
     */
    private BufferedImage parseH264Frame(byte[] h264Data) {
        try {
            // 创建临时的FFmpeg解码器来解码单个帧
            if (h264Decoder == null) {
                h264Decoder = new FFmpegFrameGrabber(new ByteArrayInputStream(h264Data));
                h264Decoder.setVideoCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_H264);
                h264Decoder.start();
            }

            // 使用ByteArrayInputStream创建临时解码器
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(h264Data)) {
                FFmpegFrameGrabber tempGrabber = new FFmpegFrameGrabber(inputStream);
                tempGrabber.setVideoCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_H264);
                tempGrabber.start();

                Frame frame = tempGrabber.grabImage();
                if (frame != null && frameConverter != null) {
                    BufferedImage bufferedImage = frameConverter.convert(frame);
                    tempGrabber.close();
                    return bufferedImage;
                }
                tempGrabber.close();
            }
        } catch (Exception e) {
            log.debug("解析H.264帧失败: {}", e.getMessage());
        }
        return null;
    }

}
