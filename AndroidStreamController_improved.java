package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Android设备视频流控制器 - 改进版本
 * 修复H.264流解码问题，确保SPS/PPS参数集正确传输
 */
@Slf4j
@RestController
@RequestMapping("/android/stream")
public class AndroidStreamController {

    private static final int BUFFER_SIZE = 8192;
    private static final int MAX_RETRY_COUNT = 3;

    @Autowired
    private DeviceManager deviceManager;

    /**
     * 启动Android设备视频流 - 改进版本
     * 修复H.264流处理，确保SPS/PPS参数集正确传输
     */
    @GetMapping("/video/{deviceId}")
    public ResponseEntity<StreamingResponseBody> streamVideo(
            @PathVariable String deviceId,
            @RequestParam(defaultValue = "0") int width,
            @RequestParam(defaultValue = "0") int height,
            @RequestParam(defaultValue = "4000000") int bitRate) {

        log.info("开始Android设备 {} 的视频流，分辨率: {}x{}, 比特率: {}", deviceId, width, height, bitRate);

        AndroidDevice device = (AndroidDevice) deviceManager.getDevice(deviceId);
        if (device == null) {
            throw new IllegalArgumentException("设备 " + deviceId + " 未找到");
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("video/h264"));
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);
        // 添加CORS头
        headers.add("Access-Control-Allow-Origin", "*");
        headers.add("Access-Control-Allow-Methods", "GET, POST, DELETE");

        StreamingResponseBody responseBody = outputStream -> {
            InputStream videoStream = null;
            int retryCount = 0;
            AtomicBoolean isStreaming = new AtomicBoolean(true);

            try {
                while (isStreaming.get() && retryCount < MAX_RETRY_COUNT) {
                    try {
                        // 启动设备视频流
                        videoStream = device.startVideoStream(width, height, bitRate);
                        log.info("设备 {} 视频流启动成功", deviceId);

                        // 改进的H.264流处理
                        ImprovedH264StreamProcessor processor = new ImprovedH264StreamProcessor();
                        byte[] buffer = new byte[BUFFER_SIZE];
                        int bytesRead;
                        long totalBytes = 0;
                        long frameCount = 0;

                        while (isStreaming.get() && (bytesRead = videoStream.read(buffer)) != -1) {
                            // 检查设备连接状态
                            if (!device.isConnected()) {
                                log.warn("设备 {} 连接断开，停止视频流", deviceId);
                                break;
                            }

                            totalBytes += bytesRead;
                            
                            // 处理H.264数据
                            processor.addData(buffer, bytesRead);

                            // 提取并发送完整的NAL单元
                            byte[] nalUnit;
                            while ((nalUnit = processor.extractCompleteNalUnit()) != null) {
                                try {
                                    // 确保NAL单元完整性
                                    if (isValidNalUnit(nalUnit)) {
                                        outputStream.write(nalUnit);
                                        outputStream.flush();
                                        frameCount++;

                                        if (frameCount % 100 == 0) {
                                            log.debug("设备 {} 已发送 {} 个NAL单元，总数据: {}KB", 
                                                    deviceId, frameCount, totalBytes / 1024);
                                        }
                                    }
                                } catch (IOException e) {
                                    log.warn("发送NAL单元失败: {}", e.getMessage());
                                    isStreaming.set(false);
                                    break;
                                }
                            }
                        }

                        log.info("设备 {} 视频流传输完成，共发送 {} 个NAL单元，总数据: {}MB", 
                                deviceId, frameCount, totalBytes / (1024 * 1024));
                        break; // 成功完成，跳出重试循环

                    } catch (IOException e) {
                        retryCount++;
                        log.warn("设备 {} 视频流传输重试 {}/{}：{}", deviceId, retryCount, MAX_RETRY_COUNT, e.getMessage());

                        if (retryCount >= MAX_RETRY_COUNT) {
                            throw new RuntimeException("视频流传输失败，重试次数已用完", e);
                        }

                        // 等待后重试
                        Thread.sleep(1000);
                    }
                }
            } catch (Exception e) {
                log.error("设备 {} 视频流传输失败: {}", deviceId, e.getMessage(), e);
                throw new RuntimeException("视频流传输失败", e);
            } finally {
                // 确保资源正确关闭
                cleanupResources(videoStream, device);
            }
        };

        return ResponseEntity.ok()
                .headers(headers)
                .body(responseBody);
    }

    /**
     * 验证NAL单元的有效性
     */
    private boolean isValidNalUnit(byte[] nalUnit) {
        if (nalUnit == null || nalUnit.length < 4) {
            return false;
        }

        // 检查NAL开始码
        boolean hasStartCode = false;
        if (nalUnit.length >= 4 && 
            nalUnit[0] == 0x00 && nalUnit[1] == 0x00 && 
            nalUnit[2] == 0x00 && nalUnit[3] == 0x01) {
            hasStartCode = true;
        } else if (nalUnit.length >= 3 && 
                   nalUnit[0] == 0x00 && nalUnit[1] == 0x00 && nalUnit[2] == 0x01) {
            hasStartCode = true;
        }

        return hasStartCode;
    }

    /**
     * 清理资源
     */
    private void cleanupResources(InputStream videoStream, AndroidDevice device) {
        try {
            if (videoStream != null) {
                videoStream.close();
            }
        } catch (IOException e) {
            log.error("关闭视频流输入流时出错: {}", e.getMessage());
        }

        try {
            device.stopVideoStream();
        } catch (Exception e) {
            log.error("停止设备视频流时出错: {}", e.getMessage());
        }
    }

    /**
     * 改进的H.264流处理器
     * 确保SPS/PPS参数集和NAL单元的完整性
     */
    private static class ImprovedH264StreamProcessor {
        private byte[] buffer = new byte[1024 * 1024]; // 1MB缓冲区
        private int bufferSize = 0;
        private static final byte[] NAL_START_CODE_4 = {0x00, 0x00, 0x00, 0x01};
        private static final byte[] NAL_START_CODE_3 = {0x00, 0x00, 0x01};
        
        // NAL单元类型常量
        private static final int NAL_TYPE_SPS = 7;  // Sequence Parameter Set
        private static final int NAL_TYPE_PPS = 8;  // Picture Parameter Set
        private static final int NAL_TYPE_IDR = 5;  // IDR frame
        
        private boolean spsReceived = false;
        private boolean ppsReceived = false;

        public void addData(byte[] data, int length) {
            // 确保缓冲区有足够空间
            if (bufferSize + length > buffer.length) {
                // 扩展缓冲区
                byte[] newBuffer = new byte[buffer.length * 2];
                System.arraycopy(buffer, 0, newBuffer, 0, bufferSize);
                buffer = newBuffer;
            }

            // 添加新数据
            System.arraycopy(data, 0, buffer, bufferSize, length);
            bufferSize += length;
        }

        public byte[] extractCompleteNalUnit() {
            if (bufferSize < 4) {
                return null;
            }

            // 查找第一个NAL开始码
            int firstNalStart = findNalStartCode(0);
            if (firstNalStart == -1) {
                return null;
            }

            // 查找下一个NAL开始码
            int nextNalStart = findNalStartCode(firstNalStart + 4);
            if (nextNalStart == -1) {
                // 没找到下一个NAL单元，检查缓冲区是否足够大
                if (bufferSize > 64 * 1024) { // 如果缓冲区超过64KB，可能是完整的NAL单元
                    byte[] nalUnit = new byte[bufferSize - firstNalStart];
                    System.arraycopy(buffer, firstNalStart, nalUnit, 0, nalUnit.length);
                    bufferSize = 0; // 清空缓冲区
                    
                    // 检查并记录NAL单元类型
                    checkNalUnitType(nalUnit);
                    return nalUnit;
                }
                return null;
            }

            // 提取NAL单元
            int nalLength = nextNalStart - firstNalStart;
            byte[] nalUnit = new byte[nalLength];
            System.arraycopy(buffer, firstNalStart, nalUnit, 0, nalLength);

            // 移除已提取的NAL单元
            System.arraycopy(buffer, nextNalStart, buffer, 0, bufferSize - nextNalStart);
            bufferSize -= nextNalStart;

            // 检查并记录NAL单元类型
            checkNalUnitType(nalUnit);
            return nalUnit;
        }

        private void checkNalUnitType(byte[] nalUnit) {
            if (nalUnit.length < 5) return;
            
            // 获取NAL单元类型（去掉开始码后的第一个字节的低5位）
            int startCodeLength = (nalUnit[2] == 0x01) ? 3 : 4;
            int nalType = nalUnit[startCodeLength] & 0x1F;
            
            switch (nalType) {
                case NAL_TYPE_SPS:
                    spsReceived = true;
                    log.debug("收到SPS参数集");
                    break;
                case NAL_TYPE_PPS:
                    ppsReceived = true;
                    log.debug("收到PPS参数集");
                    break;
                case NAL_TYPE_IDR:
                    if (spsReceived && ppsReceived) {
                        log.debug("收到IDR帧，SPS/PPS已就绪");
                    } else {
                        log.warn("收到IDR帧但缺少SPS/PPS参数集");
                    }
                    break;
            }
        }

        private int findNalStartCode(int startPos) {
            // 查找4字节开始码
            for (int i = startPos; i <= bufferSize - 4; i++) {
                if (buffer[i] == 0x00 && buffer[i + 1] == 0x00 &&
                        buffer[i + 2] == 0x00 && buffer[i + 3] == 0x01) {
                    return i;
                }
            }

            // 查找3字节开始码
            for (int i = startPos; i <= bufferSize - 3; i++) {
                if (buffer[i] == 0x00 && buffer[i + 1] == 0x00 && buffer[i + 2] == 0x01) {
                    return i;
                }
            }

            return -1;
        }
    }

    /**
     * 停止视频流
     */
    @DeleteMapping("/video/stop/{deviceId}")
    public ResponseEntity<String> stopVideo(@PathVariable String deviceId) {
        log.info("停止Android设备 {} 的视频流", deviceId);

        AndroidDevice device = (AndroidDevice) deviceManager.getDevice(deviceId);
        if (device != null) {
            device.stopVideoStream();
            return ResponseEntity.ok("视频流已停止");
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
