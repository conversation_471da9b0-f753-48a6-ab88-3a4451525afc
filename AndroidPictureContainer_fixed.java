package ui.layout.left.display.components.container.picture;

import common.constant.ResourceConstant;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import sdk.entity.AndroidDevice;
import ui.base.picture.ADBPanel;
import ui.base.picture.AndroidPictureRectDrawLabel;
import ui.base.picture.PictureRectDrawLabel;
import ui.base.picture.ScaledPoint;
import ui.entry.ClientView;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class AndroidPictureContainer extends PictureContainer implements AppObserver {
    private JLabel screenShotButton;

    // 视频流相关字段
    private InputStream videoInputStream;
    private SwingWorker<Boolean, BufferedImage> videoStreamWorker;
    private final AtomicBoolean isStreamRunning = new AtomicBoolean(false);

    public AndroidPictureContainer(Device device) {
        super(device);
    }

    @Override
    public void initComponents() {
        super.initComponents();
        screenShotButton = new JLabel();
        screenShotButton.setIcon(SwingUtil.createImageIcon(ResourceConstant.ICON_SCREENSHOT));
        screenShotButton.setToolTipText("截图");
        screenShotButton.setPreferredSize(new Dimension(30, 30));
        screenShotButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        getToolBar().add(screenShotButton);
    }

    @Override
    public PictureRectDrawLabel createPictureRectDrawLabel() {
        return new AndroidPictureRectDrawLabel();
    }

    @Override
    public JPanel createParameterPanel() {
        return new ADBPanel((AndroidDevice) getDevice());
    }

    private SwingWorker<Boolean, String> createScreenShotWorker() {
        return new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                AndroidDevice device = (AndroidDevice) getDevice();
                OperationResult result = device.screenshot();
                if (result.isSuccess()) {
                    BufferedImage image = (BufferedImage) result.getData();
                    if (image != null) {
                        SwingUtilities.invokeLater(() -> {
                            getPicturePanel().setImageStream(image);
                        });
                        return true;
                    }
                }
                return false;
            }

            @Override
            protected void done() {
                screenShotButton.setEnabled(true);
                screenShotButton.repaint();
            }
        };
    }

    @Override
    public void createActions() {
        super.createActions();
        screenShotButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (screenShotButton.isEnabled()) {
                    screenShot();
                }
            }
        });
    }

    @Override
    public boolean pictureOperationStart(OperationMethod operationMethod) {
        return true;
    }

    @Override
    public boolean pictureOperating(Operation operation) {
        return true;
    }

    @Override
    public void pictureDoubleClick(ScaledPoint point) {
    }

    public void grab() {
        AndroidDevice device = (AndroidDevice) getDevice();
        String deviceName = device.getDeviceName();

        // 如果已经有视频流在运行，先停止
        if (isStreamRunning.get()) {
            stopVideoStream();
        }

        log.info("开始启动Android设备视频流: {}", deviceName);

        videoStreamWorker = new SwingWorker<Boolean, BufferedImage>() {
            private FFmpegFrameGrabber frameGrabber;
            private Java2DFrameConverter converter;
            private boolean grabberInitialized = false;

            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    startGrab();
                    publish((BufferedImage) null); // 清空显示
                    getPicturePanel().setText("正在连接Android设备视频流: " + deviceName);

                    videoInputStream = device.videoStream();
                    if (videoInputStream == null) {
                        getPicturePanel().setText("无法启动视频流: 设备返回null");
                        return false;
                    }

                    isStreamRunning.set(true);
                    getPicturePanel().clearText();
                    completeGrab();

                    log.info("Android视频流启动成功，开始初始化H.264解码器: {}", deviceName);

                    // 初始化解码组件 - 使用单一的FFmpegFrameGrabber实例
                    frameGrabber = new FFmpegFrameGrabber(videoInputStream);
                    frameGrabber.setFormat("h264");
                    
                    // 优化的H.264解码参数
                    frameGrabber.setOption("probesize", "32768");        // 增加探测大小以更好地检测SPS/PPS
                    frameGrabber.setOption("analyzeduration", "1000000"); // 1秒分析时间
                    frameGrabber.setOption("fflags", "nobuffer+fastseek+flush_packets");
                    frameGrabber.setOption("flags", "low_delay");
                    frameGrabber.setOption("max_delay", "0");
                    frameGrabber.setOption("thread_type", "slice");
                    frameGrabber.setOption("stimeout", "5000000");       // 5秒超时
                    
                    // 启动解码器
                    frameGrabber.start();
                    grabberInitialized = true;
                    converter = new Java2DFrameConverter();

                    log.info("H.264解码器初始化成功，开始解码视频帧: {}", deviceName);
                    getPicturePanel().setText("解码器已就绪，等待视频帧...");

                    int frameCount = 0;
                    long lastFrameTime = System.currentTimeMillis();
                    
                    // 连续解码视频帧
                    while (isStreamRunning.get() && !isCancelled()) {
                        try {
                            Frame frame = frameGrabber.grab();
                            if (frame == null) {
                                // 检查是否长时间没有帧
                                if (System.currentTimeMillis() - lastFrameTime > 5000) {
                                    log.warn("超过5秒没有收到视频帧，可能流已断开: {}", deviceName);
                                    break;
                                }
                                Thread.sleep(10); // 短暂等待
                                continue;
                            }

                            // 转换并发布帧
                            BufferedImage image = converter.convert(frame);
                            if (image != null) {
                                frameCount++;
                                lastFrameTime = System.currentTimeMillis();
                                publish(image);

                                if (frameCount == 1) {
                                    log.info("成功解码第一帧: {}", deviceName);
                                    getPicturePanel().clearText();
                                } else if (frameCount % 100 == 0) {
                                    log.debug("已解码 {} 帧", frameCount);
                                }
                            }

                        } catch (Exception e) {
                            if (isStreamRunning.get()) {
                                log.warn("解码视频帧时出错: {}, 错误: {}", deviceName, e.getMessage());
                                Thread.sleep(100); // 错误后短暂休眠
                            }
                        }
                    }

                    log.info("Android视频流处理完成: {}, 共处理 {} 帧", deviceName, frameCount);
                    return true;
                } catch (Exception e) {
                    log.error("Android视频流获取失败: {}, 错误: {}", deviceName, e.getMessage(), e);
                    getPicturePanel().setText("视频流获取失败: " + e.getMessage());
                    return false;
                } finally {
                    // 清理解码器资源
                    cleanupGrabber();
                }
            }

            @Override
            protected void process(List<BufferedImage> chunks) {
                if (!chunks.isEmpty() && getPicturePanel() != null) {
                    // 只处理最新的帧，避免积压
                    BufferedImage latestFrame = chunks.get(chunks.size() - 1);
                    if (latestFrame != null) {
                        getPicturePanel().setImageStream(latestFrame);
                    }
                }
            }

            @Override
            protected void done() {
                try {
                    Boolean result = get();
                    if (!result) {
                        log.warn("Android视频流获取任务完成，但结果为失败");
                        getPicturePanel().setText("视频流启动失败，请检查设备连接状态");
                    }
                } catch (Exception e) {
                    log.error("Android视频流获取任务异常", e);
                    getPicturePanel().setText("视频流启动异常: " + e.getMessage());
                } finally {
                    stopVideoStream();
                }
            }

            /**
             * 清理FFmpegFrameGrabber资源
             */
            private void cleanupGrabber() {
                if (frameGrabber != null && grabberInitialized) {
                    try {
                        frameGrabber.stop();
                        frameGrabber.release();
                        log.info("FFmpegFrameGrabber资源已清理: {}", deviceName);
                    } catch (Exception e) {
                        log.warn("清理FFmpegFrameGrabber资源时出错: {}", e.getMessage());
                    } finally {
                        frameGrabber = null;
                        grabberInitialized = false;
                    }
                }
                
                if (converter != null) {
                    try {
                        converter.close();
                    } catch (Exception e) {
                        log.debug("关闭converter时出错: {}", e.getMessage());
                    } finally {
                        converter = null;
                    }
                }
            }
        };

        videoStreamWorker.execute();
    }

    /**
     * 简化的停止视频流方法
     */
    private void stopVideoStream() {
        isStreamRunning.set(false);

        // 关闭视频流输入流
        if (videoInputStream != null) {
            try {
                videoInputStream.close();
                log.info("Android视频流输入流已关闭: {}", getDevice().getDeviceName());
            } catch (Exception e) {
                log.warn("关闭Android视频流输入流失败: {}, 错误: {}", getDevice().getDeviceName(), e.getMessage());
            } finally {
                videoInputStream = null;
            }
        }

        // 取消工作线程
        if (videoStreamWorker != null && !videoStreamWorker.isDone()) {
            videoStreamWorker.cancel(true);
        }

        log.info("Android视频流已停止: {}", getDevice().getDeviceName());
    }

    @Override
    public void appExit() {
        stopVideoStream();
    }
}
